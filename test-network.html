<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络连接测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 网络连接测试</h1>
        
        <div id="results"></div>
        
        <button onclick="testBasicConnectivity()">测试基本连接</button>
        <button onclick="testApiHealth()">测试API健康检查</button>
        <button onclick="testVersionApi()">测试版本API</button>
        <button onclick="testActivationApi()">测试激活API</button>
        <button onclick="runAllTests()">运行所有测试</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <script>
        function addResult(title, status, message, details = null) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${status}`;
            
            let html = `<h3>${title}</h3><p>${message}</p>`;
            if (details) {
                html += `<pre>${JSON.stringify(details, null, 2)}</pre>`;
            }
            
            resultDiv.innerHTML = html;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testBasicConnectivity() {
            addResult('基本连接测试', 'info', '正在测试...');
            
            try {
                const response = await fetch('https://www.baidu.com', {
                    method: 'HEAD',
                    mode: 'no-cors',
                    signal: AbortSignal.timeout(5000)
                });
                addResult('基本连接测试', 'success', '网络连接正常');
            } catch (error) {
                addResult('基本连接测试', 'error', '网络连接失败', error.message);
            }
        }

        async function testApiHealth() {
            addResult('API健康检查', 'info', '正在测试...');
            
            try {
                const response = await fetch('https://api-g.lacs.cc/api/health', {
                    method: 'GET',
                    headers: {
                        'X-API-Key': 'your-api-key',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    signal: AbortSignal.timeout(10000)
                });

                const data = await response.json();
                
                if (response.ok) {
                    addResult('API健康检查', 'success', 'API服务器正常', data);
                } else {
                    addResult('API健康检查', 'error', `API响应错误: ${response.status}`, data);
                }
            } catch (error) {
                addResult('API健康检查', 'error', 'API连接失败', {
                    name: error.name,
                    message: error.message
                });
            }
        }

        async function testVersionApi() {
            addResult('版本API测试', 'info', '正在测试...');
            
            try {
                const url = 'https://api-g.lacs.cc/app/software/id/1/versions?limit=1&sortBy=releaseDate&sortOrder=desc&isStable=true';
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-API-Key': 'your-api-key',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    signal: AbortSignal.timeout(10000)
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('版本API测试', 'success', '版本API正常', {
                        latestVersion: data.data?.versions?.[0]?.version || '未知',
                        totalVersions: data.data?.versions?.length || 0
                    });
                } else {
                    addResult('版本API测试', 'error', `版本API错误: ${response.status}`, data);
                }
            } catch (error) {
                addResult('版本API测试', 'error', '版本API连接失败', {
                    name: error.name,
                    message: error.message
                });
            }
        }

        async function testActivationApi() {
            addResult('激活API测试', 'info', '正在测试...');
            
            try {
                const response = await fetch('https://api-g.lacs.cc/api/activation-codes/verify', {
                    method: 'POST',
                    headers: {
                        'X-API-Key': 'your-api-key',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        activationCode: 'TEST-CODE-123'
                    }),
                    signal: AbortSignal.timeout(10000)
                });

                const data = await response.json();
                
                // 即使激活码无效，只要API响应正常就算成功
                if (response.status === 400 || response.status === 404) {
                    addResult('激活API测试', 'success', '激活API正常（测试激活码无效是正常的）', data);
                } else if (response.ok) {
                    addResult('激活API测试', 'success', '激活API正常', data);
                } else {
                    addResult('激活API测试', 'error', `激活API错误: ${response.status}`, data);
                }
            } catch (error) {
                addResult('激活API测试', 'error', '激活API连接失败', {
                    name: error.name,
                    message: error.message
                });
            }
        }

        async function runAllTests() {
            clearResults();
            addResult('测试开始', 'info', '正在运行所有测试...');
            
            await testBasicConnectivity();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testApiHealth();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testVersionApi();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testActivationApi();
            
            addResult('测试完成', 'info', '所有测试已完成');
        }

        // 页面加载时显示说明
        window.onload = function() {
            addResult('测试说明', 'info', '这个页面用于测试HOUT应用的网络连接和API服务。点击上方按钮开始测试。');
        };
    </script>
</body>
</html>
