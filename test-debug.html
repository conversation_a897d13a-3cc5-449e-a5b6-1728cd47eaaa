<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HOUT 调试测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 HOUT 应用调试测试</h1>
        <p>这个页面用于测试 HOUT 应用的各个组件和服务是否正常工作。</p>

        <div class="test-section">
            <h3>📱 应用状态检查</h3>
            <div id="app-status" class="status info">检查中...</div>
            <button onclick="checkAppStatus()">重新检查应用状态</button>
        </div>

        <div class="test-section">
            <h3>🌐 网络连接测试</h3>
            <div id="network-status" class="status info">检查中...</div>
            <button onclick="testNetworkConnection()">测试网络连接</button>
        </div>

        <div class="test-section">
            <h3>🔄 版本检查测试</h3>
            <div id="version-status" class="status info">未测试</div>
            <button onclick="testVersionCheck()">测试版本检查</button>
        </div>

        <div class="test-section">
            <h3>🔑 激活服务测试</h3>
            <div id="activation-status" class="status info">未测试</div>
            <button onclick="testActivationService()">测试激活服务</button>
        </div>

        <div class="test-section">
            <h3>📋 控制台日志</h3>
            <div id="log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="test-section">
            <h3>🚀 快速操作</h3>
            <button onclick="openMainApp()">打开主应用</button>
            <button onclick="openMainAppWithSkip()">跳过检查打开应用</button>
            <button onclick="clearAllStorage()">清空本地存储</button>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 检查应用状态
        function checkAppStatus() {
            log('开始检查应用状态...');
            const statusDiv = document.getElementById('app-status');
            
            try {
                // 检查是否在 Tauri 环境中
                if (window.__TAURI__) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ 运行在 Tauri 环境中';
                    log('应用运行在 Tauri 环境中', 'success');
                } else {
                    statusDiv.className = 'status info';
                    statusDiv.textContent = '🌐 运行在浏览器环境中';
                    log('应用运行在浏览器环境中', 'info');
                }

                // 检查本地存储
                if (localStorage) {
                    log('本地存储可用', 'success');
                } else {
                    log('本地存储不可用', 'error');
                }

                // 检查 React 应用是否加载
                if (document.getElementById('root')) {
                    log('React 根元素存在', 'success');
                } else {
                    log('React 根元素不存在', 'error');
                }

            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 检查失败: ' + error.message;
                log('应用状态检查失败: ' + error.message, 'error');
            }
        }

        // 测试网络连接
        async function testNetworkConnection() {
            log('开始测试网络连接...');
            const statusDiv = document.getElementById('network-status');
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 测试中...';

            try {
                const response = await fetch('https://api-g.lacs.cc/api/health', {
                    method: 'GET',
                    headers: {
                        'X-API-Key': 'your-api-key',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    signal: AbortSignal.timeout(10000)
                });

                if (response.ok) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ API 服务器连接正常';
                    log('API 服务器连接正常', 'success');
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ API 服务器响应错误: ${response.status}`;
                    log(`API 服务器响应错误: ${response.status}`, 'error');
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 网络连接失败: ' + error.message;
                log('网络连接失败: ' + error.message, 'error');
            }
        }

        // 测试版本检查
        async function testVersionCheck() {
            log('开始测试版本检查...');
            const statusDiv = document.getElementById('version-status');
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 测试中...';

            try {
                const url = 'https://api-g.lacs.cc/app/software/id/1/versions?limit=1&sortBy=releaseDate&sortOrder=desc&isStable=true';
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-API-Key': 'your-api-key',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    signal: AbortSignal.timeout(10000)
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data && data.data.versions) {
                        statusDiv.className = 'status success';
                        statusDiv.textContent = `✅ 版本检查成功，最新版本: ${data.data.versions[0]?.version || '未知'}`;
                        log(`版本检查成功，最新版本: ${data.data.versions[0]?.version || '未知'}`, 'success');
                    } else {
                        statusDiv.className = 'status error';
                        statusDiv.textContent = '❌ 版本数据格式错误';
                        log('版本数据格式错误: ' + JSON.stringify(data), 'error');
                    }
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ 版本检查失败: ${response.status}`;
                    log(`版本检查失败: ${response.status}`, 'error');
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 版本检查异常: ' + error.message;
                log('版本检查异常: ' + error.message, 'error');
            }
        }

        // 测试激活服务
        function testActivationService() {
            log('开始测试激活服务...');
            const statusDiv = document.getElementById('activation-status');
            
            try {
                // 检查本地激活数据
                const activationData = localStorage.getItem('hout_activation_data');
                if (activationData) {
                    statusDiv.className = 'status info';
                    statusDiv.textContent = '📋 发现本地激活数据';
                    log('发现本地激活数据', 'info');
                } else {
                    statusDiv.className = 'status info';
                    statusDiv.textContent = '📋 未发现本地激活数据';
                    log('未发现本地激活数据', 'info');
                }

                // 检查应用配置
                const appConfig = localStorage.getItem('hout-app-config');
                if (appConfig) {
                    log('发现应用配置数据', 'info');
                } else {
                    log('未发现应用配置数据', 'info');
                }

            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 激活服务测试失败: ' + error.message;
                log('激活服务测试失败: ' + error.message, 'error');
            }
        }

        // 打开主应用
        function openMainApp() {
            log('打开主应用...');
            window.location.href = 'http://localhost:1420';
        }

        // 跳过检查打开应用
        function openMainAppWithSkip() {
            log('跳过检查打开应用...');
            window.location.href = 'http://localhost:1420?skip=true';
        }

        // 清空本地存储
        function clearAllStorage() {
            if (confirm('确定要清空所有本地存储数据吗？这将删除激活信息和应用配置。')) {
                localStorage.clear();
                log('已清空所有本地存储数据', 'success');
                alert('本地存储已清空');
            }
        }

        // 页面加载时自动检查
        window.onload = function() {
            log('调试页面加载完成');
            checkAppStatus();
        };
    </script>
</body>
</html>
